.theme-bg {
    background-color: var(--surface-100);
}

.theme-surface {
    background-color: var(--surface-0);
}

.theme-text {
    color: var(--text-color);
}

.theme-text-secondary {
    color: var(--text-color-secondary);
}

.theme-border {
    border: 1px solid var(--surface-border);
}

.theme-border-primary {
    border: 1px solid var(--primary-color);
}

.theme-border-rounded {
    border-radius: var(--border-radius);
}

.theme-primary-bg {
    background-color: var(--primary-color);
}

.theme-primary-text {
    color: var(--primary-color-text);
}

.theme-hover:hover {
    background-color: var(--surface-hover);
}