import { useRef, useEffect } from "react";
import { useUIStore } from "../store/useUiStore";
import Tools from "./Tools";
import Projects from "./Projects";
import ContactMe from "./ContactMe";
import Intro from "./Intro";
import { useSectionAnimation } from "../libs/context/ObserverContext";
import "./home.css";

const SECTION_HEIGHTS = {
  intro: "70vh",
  tools: "60vh",
  projects: "70vh",
  contact: "100vh",
};

export default function Home() {
  // Navigation refs for the UI store
  const introRef = useRef<HTMLElement>(null);
  const toolsUIRef = useRef<HTMLElement>(null);
  const projectsUIRef = useRef<HTMLElement>(null);
  const assetsRef = useRef<HTMLElement>(null);
  const aboutRef = useRef<HTMLElement>(null);
  const contactUIRef = useRef<HTMLElement>(null);

  // Unified observer for animations and navigation tracking
  const [introAnimRef] = useSectionAnimation("intro"); // Only need ref for navigation tracking
  const [toolsAnimRef, isToolsVisible] = useSectionAnimation("tools");
  const [projectsAnimRef, isProjectsVisible] = useSectionAnimation("projects");
  const [contactAnimRef, isContactVisible] = useSectionAnimation("contact");

  const { setRefs } = useUIStore();

  useEffect(() => {
    setRefs({
      intro: introRef,
      tools: toolsUIRef,
      projects: projectsUIRef,
      assets: assetsRef,
      about: aboutRef,
      contact: contactUIRef,
    });
  }, [setRefs]);
  return (
    <div
      className="flex flex-column justify-content-center align-items-center"
      style={{
        backgroundColor: "rgba(0, 0, 0, 0.3)",
        color: "var(--text-color)",
      }}
    >
      <div className="blurred-element-right fixed right-0" />
      <div className="blurred-element-left fixed left-0" />

      {/* Intro */}
      <section
        ref={(el) => {
          introRef.current = el;
          introAnimRef.current = el;
        }}
        id="intro"
        className="w-12 flex justify-content-center align-items-center"
        style={{ height: `${SECTION_HEIGHTS.intro}` }}
      >
        <Intro />
      </section>

      <section
        ref={(el) => {
          toolsUIRef.current = el;
          toolsAnimRef.current = el;
        }}
        id="tools"
        className={`w-12 flex justify-content-center  align-items-center
          ${isToolsVisible ? "fadein-up" : "opacity-0"
          }`}
        style={{ height: `${SECTION_HEIGHTS.tools}` }}
      >
        <Tools />
      </section>

      {/* Projects — fade from right */}
      <section
        ref={(el) => {
          projectsUIRef.current = el;
          projectsAnimRef.current = el;
        }}
        id="projects"
        className={`w-12 flex justify-content-center align-items-center 
          ${isProjectsVisible ? "fadein-up " : "opacity-0"
          }`}
        style={{ height: `${SECTION_HEIGHTS.projects}` }}
      >
        <Projects />
      </section>

      {/* Contact — fade from bottom */}
      <section
        ref={(el) => {
          contactUIRef.current = el;
          contactAnimRef.current = el;
        }}
        id="contact"
        className={`w-12 flex justify-content-center align-items-end  ${isContactVisible ? "fadein-down" : "opacity-0"
          }`}
        style={{ height: `${SECTION_HEIGHTS.contact}` }}
      >


        <ContactMe />
      </section >
    </div >
  );
}
