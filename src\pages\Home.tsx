import { useRef, useEffect } from "react";
import { useUIStore } from "../store/useUiStore";
import Tools from "./Tools";
import { Divider } from "primereact/divider";
import Intro from "./Intro";
import Projects from "./Projects";
import About from "./About";
import ContactMe from "./ContactMe";

// import { useSectionScrollNavigation } from "../hooks/useSectionScrollNavigation";

export default function Home() {
  const introRef = useRef<HTMLElement>(null);
  const toolsRef = useRef<HTMLElement>(null);
  const projectsRef = useRef<HTMLElement>(null);
  const assetsRef = useRef<HTMLElement>(null);
  const aboutRef = useRef<HTMLElement>(null);
  const contactRef = useRef<HTMLElement>(null);

  // useSectionScrollNavigation();

  const { setRefs } = useUIStore();

  useEffect(() => {
    setRefs({
      intro: introRef,
      tools: toolsRef,
      projects: projectsRef,
      assets: assetsRef,
      about: aboutRef,
      contact: contactRef,
    });
  }, [setRefs, introRef, toolsRef, projectsRef, assetsRef, aboutRef, contactRef]);

  return (
    <div
      style={{
        backgroundColor: "var(--surface-c)",
        color: "var(--text-color)",
      }}
      className="flex flex-column justify-content-center align-items-center"
    >
      <section ref={introRef} id="intro"
        style={{ height: "100vh", maxHeight: "1200px" }} >
        <Intro />
      </section>

      <Divider />

      <section ref={toolsRef} id="tools"
        style={{ minHeight: "1000px", height: "75vh", maxHeight: "1200px" }} >
        <Tools />
      </section>


      {/* <Divider />

      <section ref={assetsRef} id="assets"
        style={{ minHeight: "1000px", maxHeight: "1100px" }} >
        <h2 className="text-4xl font-bold mb-4">Assets and Widgets</h2>
        <Placeholder />
      </section> */}

      <Divider />

      <section ref={projectsRef} id="projects"
        style={{ minHeight: "1000px", maxHeight: "1200px" }} >
        <Projects />
      </section>

      <Divider />

      {/* <section ref={aboutRef} id="about" >
        <About />
      </section> */}
      <section ref={contactRef} id="contact"
        style={{ height: "75vh" }} >
        <ContactMe />
      </section>
    </div>
  );
}
