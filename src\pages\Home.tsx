import { useRef, useEffect } from "react";
import { useUIStore } from "../store/useUiStore";
import Tools from "./Tools";
import useInView from "../hooks/useinview";
import Intro from "./Intro";
import Projects from "./Projects";

import ContactMe from "./ContactMe";
import { useActiveSection } from "../hooks/useActiveSection";
import "./home.css";

// import { useSectionScrollNavigation } from "../hooks/useSectionScrollNavigation";

export default function Home() {
  // Section refs for navigation
  const introRef = useRef<HTMLElement>(null);
  const toolsRef = useRef<HTMLElement>(null);
  const projectsRef = useRef<HTMLElement>(null);
  const assetsRef = useRef<HTMLElement>(null);
  const aboutRef = useRef<HTMLElement>(null);
  const contactRef = useRef<HTMLElement>(null);

  // useInView hooks for animations
  const [toolsInViewRef, isToolsInView] = useInView<HTMLElement>();
  const [projectsInViewRef, isProjectsInView] = useInView<HTMLElement>();
  const [contactInViewRef, isContactInView] = useInView<HTMLElement>();

  // useSectionScrollNavigation();

  // Initialize active section tracking
  useActiveSection();

  const { setRefs } = useUIStore();

  useEffect(() => {
    setRefs({
      intro: introRef,
      tools: toolsRef,
      projects: projectsRef,
      assets: assetsRef,
      about: aboutRef,
      contact: contactRef,
    });
  }, [setRefs]);

  return (
    <div
      style={{
        backgroundColor: "rgba(0, 0, 0, 0.3)",
        color: "var(--text-color)",
      }}
      className="flex flex-column justify-content-center align-items-center"
    >

      <div className="blurred-element-right fixed right-0" />
      <div className="blurred-element-left fixed left-0" />



      <section ref={introRef} id="intro" className="w-12">
        <Intro />
      </section>


      <section
        ref={(el) => {
          toolsRef.current = el;
          toolsInViewRef.current = el;
        }}
        id="tools"
        className={`w-12 opacity-0 ${isToolsInView ? ' fadeindown animation-delay-300 animation-duration-1000 opacity-100' : 'opacity-0'}`}
      >
        <Tools />
      </section>

      <section
        ref={(el) => {
          projectsRef.current = el;
          projectsInViewRef.current = el;
        }}
        id="projects"
        className={`w-12 ${isProjectsInView ? 'opacity-100' : 'opacity-0'}`}
        style={{ minHeight: "1000px", maxHeight: "1200px" }}
      >
        <Projects />
      </section>


      {/* <section ref={aboutRef} id="about" >
        <About />
      </section> */}
      <section
        ref={(el) => {
          contactRef.current = el;
          contactInViewRef.current = el;
        }}
        id="contact"
        className={`w-12 ${isContactInView ? 'opacity-100' : 'opacity-0'}`}
        style={{ display: "flex", justifyContent: "center", alignItems: "flex-end", height: "80vh" }}
      >
        <ContactMe />
      </section>
    </div >
  );
}
