// import { MenubarPassThroughOptions } from "primereact/menubar";
import "primereact/menubar";

// First Option creates a custom interface and add it's declarations on top of the existing MenubarPassThroughOptions.
// This is useful if you want to extend the existing options without modifying the original interface. - dh

// export interface CustomMenubarPassThroughOptions
//   extends MenubarPassThroughOptions {
//   menuButton?: any;
//   menuIcon?: any;
// }

// Second Option uses module augmentation to add new properties. Allowing me to add properties to the existing MenubarPassThroughOptions interface without creating a new one.
// This is useful if you want to add properties to the existing interface without creating a new one - dh

declare module "primereact/menubar" {
  export interface MenubarPassThroughOptions {
    menuButton?: any;
    menuIcon?: any;
  }
}
