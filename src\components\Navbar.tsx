import { useRef, useContext } from "react";
import { useUIStore } from "../store/useUiStore";
import { But<PERSON> } from "primereact/button";
import { Menu } from "primereact/menu";
import { Menubar } from "primereact/menubar";
import { useNavigate } from "react-router";
import { PrimeReactContext } from "primereact/api";
import { createMenuItems } from "../assets/common/allmenuItems";
import Logo from "../assets/Logo";

import type { MenuItem } from "primereact/menuitem";
import type { MenubarPassThroughOptions } from "primereact/menubar";

export default function Navbar() {
  const popupMenu = useRef<Menu>(null);
  const navigate = useNavigate();

  const { scrollToSection, lightMode, handleThemeChange, activeSection } = useUIStore();

  const pt: MenubarPassThroughOptions = {
    root: {
      className: "flex h-5rem justify-content-center w-full px-5",
    },
    menu: {

      className:
        "sm:flex md:flex-row sm:w-8 w-12 gap-0 justify-content-center border-none shadow-none",
    },

    menuButton: {

      className: "custom-menu-button border-none bg-primary shadow-none",
    },
    button: {

      className: "md:hidden sm:flex ",
    },
    menuIcon: {
      className: "",
    },
    submenu: {
      style: {
        transform: "translatey(40px)"
      },
    },
    start: {
      className: "flex justify-content-center w-2",
    },
    end: {
      className: "flex justify-content-center w-2",
    },

    menuitem: (props: any) => {
      const isActive = props?.context?.item?.item?.className?.includes("active-nav-item");

      return {
        ...props,
        style: isActive ? {
          backgroundColor: "var(--primary-color)",
          borderRadius: "var(--border-radius)"
        } : {},

        className: `flex max-w-min border-round-3xl}`,
        onMouseEnter: (event: React.MouseEvent<HTMLElement>) => {

          props?.onMouseEnter?.(event);
          const submenu = (event.currentTarget as HTMLElement).querySelector(
            ".p-submenu-list"
          ) as HTMLElement | null;
          if (submenu && submenu.children.length > 0) {
            submenu.style.display = "block";
          }
        },
        onMouseLeave: (event: React.MouseEvent<HTMLElement>) => {
          props?.onMouseLeave?.(event);
          const submenu = (event.currentTarget as HTMLElement).querySelector(
            ".p-submenu-list"
          ) as HTMLElement | null;
          if (submenu && submenu.children.length > 0) {
            submenu.style.display = "none";
          }
        },
      };
    },
    label: {
      className: "text-sm flex sm:hidden lg:flex ",
    },
    icon: {
      className: "text-sm ",
    },
  };

  const { changeTheme } = useContext(PrimeReactContext) || {};

  const allMenuItems: MenuItem[] = createMenuItems(
    scrollToSection,
    navigate,
    handleThemeChange,
    changeTheme,
    activeSection,
  );

  const menubarItems = allMenuItems.filter((item) =>
    ["intro", "tools", "projects", "contact"].includes(item.key)
  );

  const popupItems = allMenuItems.filter((item) =>
    ["home", "tools", "projects", "contact", "theme"].includes(item.key)
  );

  const start = (
    <div className="flex">
      <Logo size={75} />
    </div>
  );

  const end = (
    <>
      <div className="flex  gap-3">
        <Button
          icon={lightMode ? "pi pi-sun" : "pi pi-moon"}
          onClick={() => handleThemeChange(changeTheme)}
          className="p-button-outlined p-button-sm"
        />
        <Menu
          className="flex justify-content-center"
          model={popupItems}
          popup
          ref={popupMenu}
          id="popup-menu"
        />
        <Button
          className="mr-2"
          icon="pi pi-bars"
          onClick={(e) => popupMenu?.current?.toggle(e)}
        />
      </div>
    </>
  );

  return (
    <div
      style={{
        backgroundColor: "var(--surface-100)",
        color: "var(--text-color)",
        zIndex: 99999,
      }}
      className="flex w-12 sticky top-0 justify-content-center align-items-center "
    >
      <Menubar model={menubarItems} start={start} end={end} pt={pt} />
    </div >
  );
}
