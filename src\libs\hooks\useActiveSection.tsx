import { useEffect } from "react";
import { useUIStore } from "../store/useUiStore";

/**
 * Hook to automatically track which section is currently in the viewport
 * and update the activeSection in the UI store accordingly.
 * 
 * This should be used in the main Home component where all sections are rendered.
 */
export function useActiveSection() {
  const { refs, initializeIntersectionObserver, cleanupIntersectionObserver } = useUIStore();

  useEffect(() => {
    // Initialize intersection observer when refs are available
    if (Object.keys(refs).length > 0) {
      initializeIntersectionObserver();
    }

    // Cleanup on unmount
    return () => {
      cleanupIntersectionObserver();
    };
  }, [refs, initializeIntersectionObserver, cleanupIntersectionObserver]);

  // Also cleanup when component unmounts
  useEffect(() => {
    return () => {
      cleanupIntersectionObserver();
    };
  }, [cleanupIntersectionObserver]);
}
