import { But<PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { Image } from "primereact/image";
import { useUIStore } from "../store/useUiStore";

export default function Intro() {
  const { scrollToSection } = useUIStore();

  return (
    <div
      className="flex flex-column w-10 lg:flex-row align-items-center justify-content-evenly gap-2 my-8 px-4 py-4"
      style={{
        height: "75vh",
        justifySelf: "center", background: "rgba(0, 0, 0, 0.75)", color: "var(--text-color)", borderRadius: "3rem",
        border: "2px solid white", outline: "5px solid black"
      }}
    >
      <div className="flex flex-column align-items-center sm:w-10 lg:w-6">
        <h1 className="w-9 text-xl white-space-nowrap sm:text-2xl md:text-4xl lg:text-3xl xl:text-4xl font-bold mb-1">
          Turning your complex ideas<br /> into elegant solutions
        </h1>
        <p
          className="text-sm w-9 line-height-2 lg:text-base mb-4"
          style={{ color: "var(--text-color-secondary)" }}
        >
          Hey there! I'm <PERSON>. I specialize in building clean,
          efficient, and engaging web interfaces using modern technologies and
          front-end best practices.
        </p>
        <div className="flex w-9 justify-content-center lg:justify-content-start flex-1 gap-2">
          <Button
            onClick={() => scrollToSection("projects")}
            label="View Projects"
            className="p-button-outlined p-button-primary"
          />
          <a onClick={() => window.open("/resumepdf.pdf", "_blank", "width=900,height=900, left=2000, top=300, noopener, noreferrer")} >
            <Button
              label="View Resume"
              className="p-button-text p-button-secondary"
            />
          </a>
        </div>
      </div>

      <div className="flex justify-content-center p-0 m-0 sm:w-10 lg:w-6">
        <Card className="relative w-full lg:w-full md:w-10 sm:w-11  overflow-hidden border-none shadow-5 surface-card" style={{ borderRadius: "8px" }}>

          <Image
            className=" h-auto block"
            src="https://images.unsplash.com/photo-1473830394358-91588751b241"
            alt="Profile"
            width="100%"
            height="100%"
            preview
          />

          <div className="absolute bottom-0 right-0 w-8 sm:w-8 md:w-7 lg:w-7 xl:w-6 p-3 m-3 text-white text-xs line-height-2  md:text-xs lg-text-sm xl:text-base z-2"
            style={{ color: "green", background: "rgba(0, 0, 0, 0.3)" }}>
            “Sometimes I feel like I lead an emotionally complex life, and then the sun comes out and I'm happy.
            So functionally I am no different than a big leaf.” – Some Influencer
          </div>
        </Card>

      </div>
    </div>
  );
}
