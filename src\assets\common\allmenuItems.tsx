// menuItems.ts
import type { MenuItem } from "primereact/menuitem";
import { useUIStore } from "../../store/useUiStore";


export function createMenuItems(
    scrollToSection: (key: "intro" | "tools" | "projects" | "assets" | "about" | "contact" | "projects-pokemon" | "projects-coinbase") => void,
    navigate: (to: string) => void,
    handleThemeChange: (changeTheme?: (current: string, next: string, linkElementId: string) => void) => void,
    changeTheme?: (current: string, next: string, linkElementId: string) => void,
    activeSection?: string


): MenuItem[] {

    const { lightMode } = useUIStore();


    return [
        {
            key: "intro",
            label: "Intro",
            icon: "pi pi-fw pi-id-card",
            command: () => scrollToSection("intro"),
            className: activeSection === "intro" ? "active-nav-item" : "",
        },
        {
            key: "tools",
            label: "Tools",
            icon: "pi pi-fw pi-wrench",
            command: () => scrollToSection("tools"),
            className: activeSection === "tools" ? "active-nav-item" : "",
        },
        {
            key: "assets",
            label: "Assets",
            icon: "pi pi-fw pi-user",
            command: () => scrollToSection("assets"),
            className: activeSection === "assets" ? "active-nav-item" : "",
        },
        {
            key: "projects",
            label: "Projects",
            icon: "pi pi-fw pi-folder",
            className: activeSection === "projects" ? "active-nav-item" : "",
            command: () => scrollToSection("projects"),
            items: [
                {
                    key: "pokemon",
                    label: "Pokemon",
                    icon: "pi pi-ball",
                    command: () => scrollToSection("projects"),
                    className: ""
                },
                {
                    key: "coinbase",
                    label: "CoinBase", icon: "pi pi-currency",
                    command: () => scrollToSection("projects"),
                    className: ""
                },
            ],
        },
        {
            key: "about",
            label: "About",
            icon: "pi pi-fw pi-envelope",
            command: () => scrollToSection("about"),
            className: activeSection === "about" ? "active-nav-item" : "",
        },
        {

            key: "contact",
            label: "Contact",
            icon: "pi pi-fw pi-address-book",
            command: () => scrollToSection("contact"),
            className: activeSection === "contact" ? "active-nav-item" : "",
        },
        {
            key: "theme",
            label: "Theme Change",
            className: "",
            items: [
                {
                    key: "themeswap",
                    label: lightMode ? "Light" : "Dark",
                    icon: lightMode ? "pi pi-fw pi-sun" : "pi pi-fw pi-moon",
                    command: () => {

                        handleThemeChange(changeTheme)
                    },
                    className: ""
                },

            ]

        }
    ];
}