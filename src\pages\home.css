.blurred-element-left {
    mask-image: linear-gradient(to right, black 20%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.4) 100%);
    -webkit-mask-image: linear-gradient(to right, black 20%, rgba(0, 0, 0, 0.8), 70% rgba(0, 0, 0, 0.4) 100%);

    backdrop-filter: blur(15px);
    background-color: rgba(0, 0, 0, 0.5);
    width: 20vw;
    min-height: 100vh;
    overflow: visible;
    top: 0;
    z-index: 19999;
}

.blurred-element-right {
    mask-image: linear-gradient(to left, black 20%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.4) 100%);
    -webkit-mask-image: linear-gradient(to left, black 20%, rgba(0, 0, 0, 0.8), 50% rgba(0, 0, 0, 0.4) 100%);

    backdrop-filter: blur(15px);
    background-color: rgba(0, 0, 0, 0.4);
    width: 20vw;
    min-height: 100vh;
    overflow: visible;
    top: 0;
    z-index: 19999;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(540px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-540px);
    }

    100% {
        opacity: 1;
        transform: translateY(0px);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        transform: translateX(-40px);
    }

    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(40px);
    }

    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.fadein-up {
    animation: fadeInUp ease-out 1;
    animation-duration: 1s;
}

.fadein-down {
    animation: fadeInDown ease-out 1;
    animation-duration: 1s;
}

.fadein-left {
    animation: fadeInLeft ease-out 1;
    animation-duration: 1s;
}

.fadein-right {
    animation: fadeInRight ease-out 1;
    animation-duration: 1s;
}