.blurred-element-left {
    mask-image: linear-gradient(to right, black 20%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.4) 100%);
    -webkit-mask-image: linear-gradient(to right, black 20%, rgba(0, 0, 0, 0.8), 70% rgba(0, 0, 0, 0.4) 100%);

    backdrop-filter: blur(15px);
    background-color: rgba(0, 0, 0, 0.4);
    width: 20vw;
    min-height: 100vh;
    overflow: visible;
    top: 0;
    z-index: 19999;
    /* Adjust the blur radius as needed */
}

.blurred-element-right {
    mask-image: linear-gradient(to left, black 20%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.4) 100%);
    -webkit-mask-image: linear-gradient(to left, black 20%, rgba(0, 0, 0, 0.8), 50% rgba(0, 0, 0, 0.4) 100%);

    backdrop-filter: blur(15px);
    background-color: rgba(0, 0, 0, 0.4);
    width: 20vw;
    min-height: 100vh;
    overflow: visible;
    top: 0;
    z-index: 19999;
    /* Adjust the blur radius as needed */
}