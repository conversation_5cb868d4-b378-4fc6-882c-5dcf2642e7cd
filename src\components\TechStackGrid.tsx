
import { imageList } from "../assets/images/ImageList";
import TechItem from "./TechItems";
import { Divider } from "primereact/divider";

export default function TechStackGrid() {
    return (
        <>
            <h2 className="flex w-12 text-center justify-content-center text-2xl font-bold m-0 mb-1">Tech Stack</h2>
            <Divider className="mt-0 pt-0 mb-3" />
            <div className="grid col-12 grid-nogutter justify-content-evenly gap-3">
                {imageList.map((item) => (
                    <TechItem key={item.name} item={item} />
                ))}
            </div>
        </>
    );
}
