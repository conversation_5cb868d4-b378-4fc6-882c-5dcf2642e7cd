import { createBrowserRouter } from "react-router";
import ContactMe from "./pages/ContactMe";
import Tools from "./pages/Tools";
import Projects from "./pages/Projects";
import Assets from "./pages/Assets";
import Home from "./pages/Home";
import Intro from "./pages/Intro";

import App from "./App";


export const router = createBrowserRouter([
    {
        path: "/",
        element: <App />,
        children: [
            {
                index: true,
                element: <Home />,
            },
            {
                path: "Home",
                element: <Home />,
            },
            {
                path: "Intro",
                element: <Intro />,
            },
            {
                path: "Contact",
                element: <ContactMe />,
            },
            {
                path: "Assets",
                element: <Assets />,
            },
            {
                path: "Projects",
                element: <Projects />,
            },
            {
                path: "Tools",
                element: <Tools />,
            },
        ],
    },
]);

