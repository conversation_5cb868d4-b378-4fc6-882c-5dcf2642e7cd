import { useEffect, useRef, useState } from "react";
import { useUIStore } from "../store/useUiStore";
import type { ObserverConfig } from "../libs/types";



export function useUnifiedSectionObserver() {
    const { setActiveSection } = useUIStore();
    const [sectionStates, setSectionStates] = useState<Record<string, boolean>>({});
    const observerRef = useRef<IntersectionObserver | null>(null);
    const sectionsRef = useRef<Map<string, HTMLElement>>(new Map());

    const registerSection = (id: string, element: HTMLElement | null) => {
        if (!element) return;

        sectionsRef.current.set(id, element);

        if (observerRef.current) {
            observerRef.current.observe(element);
        }
    };
    const getSectionState = (id: string): boolean => {
        return sectionStates[id] || false;
    };

    useEffect(() => {
        let debounceTimer: number;

        // Handle scroll to bottom detection
        const handleScroll = () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // Check if we're at or near the bottom of the page
            const isAtBottom = scrollTop + windowHeight >= documentHeight - 50; // 50px buffer

            if (isAtBottom) {
                // Force hide projects section when at bottom
                setSectionStates(prev => ({
                    ...prev,
                    projects: false // Force projects to fade out at bottom
                }));
            }
        };

        observerRef.current = new IntersectionObserver(
            (entries) => {
                const updates: Record<string, boolean> = {};
                let mostVisibleEntry: IntersectionObserverEntry | null = null;
                let maxRatio = 0;

                entries.forEach((entry) => {
                    const sectionId = entry.target.id;
                    const isVisible = entry.isIntersecting && entry.intersectionRatio > 0.01; // Very low threshold = fade in much earlier
                    updates[sectionId] = isVisible;

                    if (entry.intersectionRatio > maxRatio) {
                        maxRatio = entry.intersectionRatio;
                        mostVisibleEntry = entry;
                    }
                });

                setSectionStates(prev => ({ ...prev, ...updates }));
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    if (mostVisibleEntry && maxRatio > 0.6) {
                        const activeId = mostVisibleEntry.target.id as any;
                        setActiveSection(activeId);
                    }
                }, 100);
            },
            {
                threshold: [0, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
                rootMargin: "150px 0px -300px 0px" // Much larger negative bottom = Projects fades out much sooner
            }
        );

        // Add scroll listener for bottom detection
        window.addEventListener('scroll', handleScroll);
        handleScroll(); // Check initial state


        sectionsRef.current.forEach((element) => {
            observerRef.current?.observe(element);
        });

        return () => {
            clearTimeout(debounceTimer);
            observerRef.current?.disconnect();
        };
    }, [setActiveSection]);

    return {
        registerSection,
        getSectionState,
        sectionStates
    };
}

export function useSectionAnimation(sectionId: string) {
    const { registerSection, getSectionState } = useUnifiedSectionObserver();
    const elementRef = useRef<HTMLElement>(null);

    useEffect(() => {
        if (elementRef.current) {
            registerSection(sectionId, elementRef.current);
        }
    }, [sectionId, registerSection]);

    return [elementRef, getSectionState(sectionId)] as const;
}


export function useOnceAnimation(config: ObserverConfig = {}) {
    const ref = useRef<HTMLElement>(null);
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting && !isVisible) {
                    setIsVisible(true);
                    if (config.triggerOnce !== false) {
                        observer.unobserve(entry.target);
                    }
                }
            },
            {
                threshold: config.threshold || 0.3,
                rootMargin: config.rootMargin || "0px 0px -100px 0px"
            }
        );

        if (ref.current) {
            observer.observe(ref.current);
        }

        return () => observer.disconnect();
    }, [config.threshold, config.rootMargin, config.triggerOnce, isVisible]);

    return [ref, isVisible] as const;
}

export function useSectionScrollNavigation() {
    const { refs, scrollToSection, setActiveSection } = useUIStore();
    const sectionKeys = Object.keys(refs) as (keyof typeof refs)[];
    const currentIndexRef = useRef(0);
    const isThrottledRef = useRef(false);

    useEffect(() => {
        const handleWheel = (e: WheelEvent) => {
            if (isThrottledRef.current || sectionKeys.length === 0) return;

            const direction = e.deltaY > 0 ? 1 : -1;
            let newIndex = currentIndexRef.current + direction;

            if (newIndex < 0) newIndex = 0;
            if (newIndex >= sectionKeys.length) newIndex = sectionKeys.length - 1;

            if (newIndex !== currentIndexRef.current) {
                const key = sectionKeys[newIndex];
                scrollToSection(key);
                setActiveSection(key);
                currentIndexRef.current = newIndex;

                isThrottledRef.current = true;
                setTimeout(() => {
                    isThrottledRef.current = false;
                }, 1200);
            }

            e.preventDefault();
        };

        window.addEventListener("wheel", handleWheel, { passive: false });

        return () => {
            window.removeEventListener("wheel", handleWheel);
        };
    }, [refs, scrollToSection]);
}

export function useInView<T extends Element = Element>(
    options?: IntersectionObserverInit
) {
    const ref = useRef<T | null>(null);
    const [hasBeenInView, setHasBeenInView] = useState<boolean>(false);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setHasBeenInView(true);
                    observer.unobserve(entry.target);
                }
            },
            {
                threshold: 0.3,
                rootMargin: "0px 0px -100px 0px",
                ...options,
            }
        );

        if (ref.current) {
            observer.observe(ref.current);
        }

        return () => observer.disconnect();
    }, [options]);

    return [ref, hasBeenInView] as const;
}



export function useScrollAnimation(triggerOffset: number = 300) {
    const [isVisible, setIsVisible] = useState(false);
    const elementRef = useRef<HTMLElement>(null);

    useEffect(() => {
        const handleScroll = () => {
            if (!elementRef.current) return;

            const rect = elementRef.current.getBoundingClientRect();
            const windowHeight = window.innerHeight;

            const shouldShow = rect.top < windowHeight - triggerOffset && rect.bottom > triggerOffset;

            setIsVisible(shouldShow);
        };

        window.addEventListener('scroll', handleScroll);
        handleScroll(); // Check initial state

        return () => window.removeEventListener('scroll', handleScroll);
    }, [triggerOffset]);

    return [elementRef, isVisible] as const;
}


