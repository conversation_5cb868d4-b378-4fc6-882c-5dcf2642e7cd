import { Button } from "primereact/button";

const emailAddress = "mailto:<EMAIL>?subject=Portfolio%20Inquiry&body=Hi%20Daniel%2C%0A%0AI%20wanted%20to%20ask..."
const emailExtension = "?subject=Portfolio%20Inquiry&body=Hi%20Daniel%2C%0A%0AI%20wanted%20to%20ask..."
const phoneNumber = "13174371432"

export default function ContactMe() {
  return (
    <div className="h-30rem px-8 text-center my-8 "
      style={{
        background: "rgba(0, 0, 0, 0.75)", color: "var(--text-color)", borderRadius: "5px",
        border: "4px solid var(--primary-color)", outline: "5px solid rgba(0,0,0,0.5)"
      }}>
      <h1 className="text-3xl font-bold mb-3">Contact Me</h1>
      <p>Lets see where we can go together.</p>

      <div className="flex flex-row align-items-center w-12 gap-3 mb-3">
        <h2 className="font-weight-bold">Email: </h2>
        <h2 className="font-normal"><EMAIL></h2>
        <Button className="h-3rem shadow-5" onClick={(e) => {
          e.preventDefault();
          window.location.href = `mailto:${emailAddress} ${emailExtension}`
        }
        }>

          Send Email
        </Button>
      </div>
      <div className="flex flex-row w-12 gap-3">
        <h2 className="font-weight-bold" >Text: </h2>
        <a href={`tel:+ ${phoneNumber}`}>
          <h2 className="font-normal">{phoneNumber}</h2>
        </a>
        {/* <Button onClick={(e) => {
          e.preventDefault();
          window.location.href = 'mailto:<EMAIL>?subject=Portfolio%20Inquiry&body=Hi%20Daniel%2C%0A%0AI%20wanted%20to%20ask...'
        }
        }>

          Send Email
        </Button> */}
      </div>

    </div >
  );
}
