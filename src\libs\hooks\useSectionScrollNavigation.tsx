import { useEffect, useRef } from "react";
import { useUIStore } from "../../store/useUiStore";

export function useSectionScrollNavigation() {
    const { refs, scrollToSection, setActiveSection } = useUIStore();
    const sectionKeys = Object.keys(refs) as (keyof typeof refs)[];
    const currentIndexRef = useRef(0);
    const isThrottledRef = useRef(false);

    useEffect(() => {
        const handleWheel = (e: WheelEvent) => {
            if (isThrottledRef.current || sectionKeys.length === 0) return;

            const direction = e.deltaY > 0 ? 1 : -1;
            let newIndex = currentIndexRef.current + direction;

            if (newIndex < 0) newIndex = 0;
            if (newIndex >= sectionKeys.length) newIndex = sectionKeys.length - 1;

            if (newIndex !== currentIndexRef.current) {
                const key = sectionKeys[newIndex];
                scrollToSection(key);
                setActiveSection(key);
                currentIndexRef.current = newIndex;

                isThrottledRef.current = true;
                setTimeout(() => {
                    isThrottledRef.current = false;
                }, 1200);
            }

            e.preventDefault();
        };

        window.addEventListener("wheel", handleWheel, { passive: false });

        return () => {
            window.removeEventListener("wheel", handleWheel);
        };
    }, [refs, scrollToSection]);
}