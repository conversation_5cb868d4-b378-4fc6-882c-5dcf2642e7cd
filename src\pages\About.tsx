

export default function About() {

    return (
        <div className="flex flex-column h-screen align-items-center text-3xl py-1 text-center w-full  gap-2 px-2">
            <h3 className="text-3xl  mb-3"> Hi, I'm <PERSON>. Here is a Little Info about me.</h3>
            <section className="flex flex-column sm:w-11 md:w-10 lg:w-9 xl:w-8  text-lg w-full">

                <details open className="flex flex-column align-items-center  pt-3 text-2xl font-bold">
                    <summary className="theme-bg theme-hover w-25rem border-round-3xl shadow-4 cursor-pointer">My Past Experiences</summary>
                    <p className="theme-bg theme-border border-round-lg shadow-5 font-medium text-lg  p-3 text-left">
                        From Director of Event Security at GainsBridge FieldHouse, Plumber, HelpDesk Team Lead, MEP draftsmen, to stay at home father. I've had quite the journey over my 36 years. I've spent my life learning new skillsets and trying to find the right fit for myself. I believe I finally have.
                    </p>
                </details>
                <details className="flex flex-column align-items-center pt-3 text-2xl font-bold">
                    <summary className="theme-bg theme-hover w-25rem border-round-3xl shadow-4 cursor-pointer">Recent Web/Dev</summary>
                    <p id="recent" className=" theme-bg theme-border border-round-lg shadow-5 font-medium text-lg p-3 text-left">
                        Over the last 9 months I have dedicated as much of my time as I can to learning JavaScript and React, as well as various component and styling libraries. I have built a couple projects along the way to practice just a few of the myriads of design philosophies, project structures, and concepts on Component Driven Development.
                    </p>
                </details>
                <details className="flex flex-column align-items-center pt-3 text-2xl font-bold">
                    <summary className="theme-bg theme-hover w-25rem border-round-3xl shadow-4 cursor-pointer">The Here and Now</summary>
                    <p id="recent" className=" theme-bg theme-border border-round-lg shadow-5 font-medium text-lg p-3 text-left">
                        While there is still so much to learn about, I'm excited to gain this experience and expertise as part of a team, one that I can bounce ideas off of and really hone my understanding and skills as they relate to Web Development.
                    </p>
                </details>

            </section>
        </div>
    );
};


