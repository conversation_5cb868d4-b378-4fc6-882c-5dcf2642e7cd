import { useEffect, useRef, useState } from "react";


// Simplified hook for static height elements
export function useInView<T extends Element = Element>(
  options?: IntersectionObserverInit
) {
  const ref = useRef<T | null>(null);
  const [hasBeenInView, setHasBeenInView] = useState<boolean>(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setHasBeenInView(true);
          observer.unobserve(entry.target);
        }
      },
      {
        threshold: 0.3,
        rootMargin: "0px 0px -100px 0px",
        ...options,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [options]);

  return [ref, hasBeenInView] as const;
}


// Alternative: Hook for scroll-based animations with fixed heights
export function useScrollAnimation(triggerOffset: number = 300) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!elementRef.current) return;

      const rect = elementRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Element is visible when its top is within the trigger zone
      const shouldShow = rect.top < windowHeight - triggerOffset && rect.bottom > triggerOffset;

      setIsVisible(shouldShow);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial state

    return () => window.removeEventListener('scroll', handleScroll);
  }, [triggerOffset]);

  return [elementRef, isVisible] as const;
}


