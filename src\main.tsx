import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider } from 'react-router'
import { PrimeReactProvider } from 'primereact/api'
import { router } from './Routes'
import './index.css'
import "./assets/common/utils/theme-utils.css"


import 'primereact/resources/primereact.min.css'
import 'primeicons/primeicons.css'
import "primeflex/primeflex.css"


createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <PrimeReactProvider>
      <RouterProvider router={router} />
    </PrimeReactProvider>
  </StrictMode>,
)
