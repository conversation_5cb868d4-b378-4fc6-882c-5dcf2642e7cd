:root {
  box-sizing: border-box;

  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  padding: 0;
  margin: 0;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  scrollbar-width: none;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  display: flex;
  justify-self: center;
  place-items: center;
  max-width: 1600px;
  width: 100vw;
  min-width: 375px;
  min-height: 100vh;
}

/* Navbar z-index overrides */
.p-menubar {
  z-index: 99999 !important;
}

/* Ensure navbar container has highest z-index */
.navbar-container {
  z-index: 99999 !important;
  position: sticky !important;
  top: 0 !important;
}

/* Override PrimeReact component z-indexes that might conflict */
.p-menu {
  z-index: 100000 !important;
}

.p-dialog {
  z-index: 100001 !important;
}

.p-tooltip {
  z-index: 100002 !important;
}