:root {
  box-sizing: border-box;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  padding: 0;
  margin: 0;
  background-image: url("/HiketoMachuCroppedMono.webp");
  background-repeat: no-repeat;
  width: 100vw;
  background-size: cover;
  background-position: center;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  scrollbar-width: none;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  display: flex;
  flex-direction: column;
  justify-self: center;
  align-self: center;
  align-items: center;
  width: 60vw;
  margin: 0;
  padding: 0;
  place-items: center;

}

/* Navbar z-index overrides */
.p-menubar {
  z-index: 99999 !important;
}

/* Ensure navbar container has highest z-index */
.navbar-container {
  z-index: 99999 !important;
  position: sticky !important;
  top: 0 !important;
}

/* Override PrimeReact component z-indexes that might conflict */
.p-menu {
  z-index: 100000 !important;
}

.p-dialog {
  z-index: 100001 !important;
}

.p-tooltip {
  z-index: 100002 !important;
}

/* Active navigation item styling */
.active-nav-item {
  background-color: var(--primary-color) !important;
  border-radius: var(--border-radius) !important;
  color: var(--primary-color-text) !important;
}

.active-nav-item .p-menuitem-text,
.active-nav-item .p-menuitem-icon {
  color: var(--primary-color-text) !important;
}

/* Smooth transitions for section animations */
section {
  transition: opacity 0.6s ease-in-out, transform 0.6s ease-in-out;
}

/* Prevent flickering during animations */
.opacity-0 {
  opacity: 0 !important;
  transform: translateY(20px);
}

.opacity-100 {
  opacity: 1 !important;
  transform: translateY(0);
}