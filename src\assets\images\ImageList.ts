import PrimeFlex from "./PrimeFlex";
import type { TechStackItem } from "../../libs/types/common";

export const imageList: TechStackItem[] = [
    {
        name: "React JS",
        src: "https://www.svgrepo.com/show/355190/reactjs.svg"
    },
    {
        name: "JavaScript",
        src: "https://www.svgrepo.com/show/355081/js.svg"
    },
    {
        name: "TypeScript",
        src: "https://www.svgrepo.com/show/349540/typescript.svg"
    },
    {
        name: "Vite",
        src: "public/vite.svg"
    },
    {
        name: "NodeJs",
        src: "https://www.svgrepo.com/show/354119/nodejs-icon.svg"
    },
    {
        name: "Git",
        src: "https://www.svgrepo.com/show/353782/git-icon.svg"
    },
    {
        name: "React Router",
        src: "https://www.svgrepo.com/show/354262/react-router.svg"
    },
    {
        name: "<PERSON>ustand",
        src: "https://user-images.githubusercontent.com/958486/218346783-72be5ae3-b953-4dd7-b239-788a882fdad6.svg"
    },
    {
        name: "PrimeReact",
        src: "https://www.primefaces.org/presskit/primereact-logo.png"
    },
    {
        name: "PrimeFlex",
        icon: PrimeFlex
    },
    {
        name: "Material UI",
        src: "https://www.svgrepo.com/show/330899/materialui.svg"
    },
    {
        name: "Chart.JS",
        src: "https://www.chartjs.org/img/chartjs-logo.svg"
    },
    {
        name: "Figma",
        src: "https://www.svgrepo.com/show/448222/figma.svg"
    },

];


