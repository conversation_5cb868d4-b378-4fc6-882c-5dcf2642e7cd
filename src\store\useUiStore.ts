// useUiStore.ts
import { create } from "zustand";
import type { RefObject } from "react";

interface UIState {
  refs: {
    intro?: RefObject<HTMLElement | null>;
    tools?: RefObject<HTMLElement | null>;
    projects?: RefObject<HTMLElement | null>;
    assets?: RefObject<HTMLElement | null>;
    about?: RefObject<HTMLElement | null>;
    contact?: RefObject<HTMLElement | null>;
  };
  isScrolling: boolean;
  setIsScrolling: (scrolling: boolean) => void;
  setRefs: (refs: UIState["refs"]) => void;
  scrollToSection: (key: keyof UIState["refs"]) => void;

  activeSection: keyof UIState["refs"];
  setActiveSection: (key: keyof UIState["refs"]) => void;



  // Theme management
  lightMode: boolean;
  setLightMode: (lightMode: boolean) => void;
  updateTheme: (
    lightMode: boolean,
    changeTheme?: (current: string, next: string, linkElementId: string) => void
  ) => void;
  handleThemeChange: (
    changeTheme?: (current: string, next: string, linkElementId: string) => void
  ) => void;
}



export const useUIStore = create<UIState>((set, get) => ({
  refs: {},
  lightMode: true,
  activeSection: "intro",
  isScrolling: false,
  setIsScrolling: (scrolling) => set({ isScrolling: scrolling }),

  setRefs: (refs) => {
    set({ refs });
  },

  scrollToSection: (key) => {
    const { setActiveSection } = get();
    const targetRef = get().refs[key];
    if (targetRef?.current) {
      const yOffset = 100;
      const element = targetRef.current;
      const y =
        element.getBoundingClientRect().top + window.pageYOffset - yOffset;

      window.scrollTo({ top: y + 0, behavior: "smooth" });
    }
    setActiveSection(key)
  },

  setActiveSection: (key) => {
    console.log("Setting active section to", key);
    set({ activeSection: key });
  },



  setLightMode: (lightMode) => set({ lightMode }),
  updateTheme: (lightMode, changeTheme) => {
    if (!changeTheme) return;
    changeTheme(
      lightMode ? "lara-light-purple" : "lara-dark-purple",
      lightMode ? "lara-dark-purple" : "lara-light-purple",
      "theme-link"
    );
  },
  handleThemeChange: (changeTheme) => {
    const currentLightMode = get().lightMode;
    const nextLightMode = !currentLightMode;
    set({ lightMode: nextLightMode });
    get().updateTheme(nextLightMode, changeTheme);
  },
}));
