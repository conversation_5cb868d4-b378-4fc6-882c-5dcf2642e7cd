import { useEffect, useRef, useState } from "react";
import { useUIStore } from "../../store/useUiStore";

interface ObserverConfig {
  threshold?: number | number[];
  rootMargin?: string;
  triggerOnce?: boolean;
}



/**
 * Unified intersection observer that handles both:
 * 1. Active section tracking for navigation
 * 2. Animation triggers for individual sections
 */
export function useUnifiedSectionObserver() {
  const { setActiveSection } = useUIStore();
  const [sectionStates, setSectionStates] = useState<Record<string, boolean>>({});
  const observerRef = useRef<IntersectionObserver | null>(null);
  const sectionsRef = useRef<Map<string, HTMLElement>>(new Map());

  // Register a section for observation
  const registerSection = (id: string, element: HTMLElement | null) => {
    if (!element) return;

    sectionsRef.current.set(id, element);

    if (observerRef.current) {
      observerRef.current.observe(element);
    }
  };

  // Get animation state for a specific section
  const getSectionState = (id: string): boolean => {
    return sectionStates[id] || false;
  };

  useEffect(() => {
    // Create unified intersection observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const updates: Record<string, boolean> = {};
        let mostVisibleEntry: IntersectionObserverEntry | null = null;
        let maxRatio = 0;

        entries.forEach((entry) => {
          const sectionId = entry.target.id;
          const isVisible = entry.isIntersecting && entry.intersectionRatio > 0.3;

          // Update animation states
          updates[sectionId] = isVisible;

          // Track most visible section for navigation
          if (entry.intersectionRatio > maxRatio) {
            maxRatio = entry.intersectionRatio;
            mostVisibleEntry = entry;
          }
        });

        // Update animation states
        setSectionStates(prev => ({ ...prev, ...updates }));

        // Update active section for navigation (only if significantly visible)
        if (mostVisibleEntry && maxRatio > 0.5) {
          const activeId = mostVisibleEntry.target.id as any;
          setActiveSection(activeId);
        }
      },
      {
        threshold: [0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.0],
        rootMargin: "-50px 0px -50px 0px"
      }
    );

    // Observe existing sections
    sectionsRef.current.forEach((element) => {
      observerRef.current?.observe(element);
    });

    return () => {
      observerRef.current?.disconnect();
    };
  }, [setActiveSection]);

  return {
    registerSection,
    getSectionState,
    sectionStates
  };
}

/**
 * Hook for individual section animation with unified observer
 */
export function useSectionAnimation(sectionId: string) {
  const { registerSection, getSectionState } = useUnifiedSectionObserver();
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      registerSection(sectionId, elementRef.current);
    }
  }, [sectionId, registerSection]);

  return [elementRef, getSectionState(sectionId)] as const;
}

/**
 * Simplified hook for one-time animations (fire once and stay visible)
 */
export function useOnceAnimation(config: ObserverConfig = {}) {
  const ref = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          if (config.triggerOnce !== false) {
            observer.unobserve(entry.target);
          }
        }
      },
      {
        threshold: config.threshold || 0.3,
        rootMargin: config.rootMargin || "0px 0px -100px 0px"
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [config.threshold, config.rootMargin, config.triggerOnce, isVisible]);

  return [ref, isVisible] as const;
}
