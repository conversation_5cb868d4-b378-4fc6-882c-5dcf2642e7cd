import useInView from "../hooks/useinview";


import type { TechStackItem } from "../types/common";

const randomFade = (): "fadeinright" | "fadeinleft" =>
    Math.random() < 0.5 ? "fadeinright" : "fadeinleft";

export default function TechItem({ item }: { item: TechStackItem; index: number }) {
    const [ref, isInView] = useInView<HTMLDivElement>();
    const animation = isInView ? randomFade() : "";

    return (
        <div
            ref={ref}
            className={`
                col-3 opacity-0 h-9rem flex flex-column align-items-center justify-content-center text-center p-3
                hover:scale-50 transition-transform transition-duration-1000
                ${isInView ? `${animation} animation-duration-500 opacity-100` : `opacity-0`}
            `}
        >
            {item.src ? (
                <img
                    src={item.src}
                    alt={item.name}
                    className="w-4rem h-auto"
                    onError={() => {
                        <p>No Image Available</p>
                    }}
                />
            ) : item.icon ? (
                <item.icon size={80} />
            ) : null}
            <span className="mt-2 text-sm">{item.name}</span>
        </div>
    );
}