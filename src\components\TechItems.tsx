import type { TechStackItem } from "../types/common";

export default function TechItem({ item }: { item: TechStackItem }) {
    return (
        <div className="col-2 h-9rem flex flex-column align-items-center justify-content-center text-center p-3">
            <div className="flex flex-column align-items-center">
                {item.src ? (
                    <img
                        src={item.src}
                        alt={item.name}
                        className="w-4rem h-auto"
                        onError={() => {
                            <p>No Image Available</p>
                        }}
                    />
                ) : item.icon ? (
                    <item.icon size={80} />
                ) : null}
                <span className="mt-2 text-lg font-bold">{item.name}</span>
            </div>
        </div>
    );
}