import useInView from "../hooks/useinview";


import type { TechStackItem } from "../types/common";



export default function TechItem({ item }: { item: TechStackItem }) {
    const [ref, isInView] = useInView<HTMLDivElement>();


    return (
        <div
            ref={ref}
            className={`
                col-2 opacity-0 h-9rem flex flex-column align-items-center justify-content-center text-center p-3
                fadeindown animation-delay-300 animation-duration-1000 ${isInView} && opacity-100
            `}
        >
            <div className="flex flex-column align-items-center fadeindown animation-delay-300 animation-duration-2000 opacity-100">
                {item.src ? (

                    <img
                        src={item.src}
                        alt={item.name}
                        className="w-4rem h-auto "
                        onError={() => {
                            <p>No Image Available</p>
                        }}
                    />

                ) : item.icon ? (
                    <item.icon size={80} />
                ) : null}
                <span className="mt-2 text-lg font-bold">{item.name}</span>
            </div>
        </div>
    );
}