export default function Logo({ size = 100, color = "var(--primary-color)" }: { size?: number, color?: string }) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
            fill={color}
            width={size}
            height={size}
        >
            <g>
                <ellipse
                    cx="256"
                    cy="256"
                    rx="180"
                    ry="80"
                    transform="rotate(0 256 256)"
                    stroke={color}
                    strokeWidth="16"
                    fill="none"
                />
                <ellipse
                    cx="256"
                    cy="256"
                    rx="180"
                    ry="80"
                    transform="rotate(60 256 256)"
                    stroke={color}
                    strokeWidth="16"
                    fill="none"
                />
                <ellipse
                    cx="256"
                    cy="256"
                    rx="180"
                    ry="80"
                    transform="rotate(120 256 256)"
                    stroke={color}
                    strokeWidth="16"
                    fill="none"
                />
                <polygon
                    points="256,200 288,216 304,256 288,296 256,312 224,296 208,256 224,216"
                    fill={color}
                />
                <text
                    x="256"
                    y="266"
                    textAnchor="middle"
                    fontFamily="Arial, sans-serif"
                    fontSize="48"
                    fontWeight="bold"
                    fill="#ffffff"
                >

                </text>
            </g>
        </svg>
    );
}
