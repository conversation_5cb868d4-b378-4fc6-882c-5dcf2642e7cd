import React, { createContext, useContext, useRef, useEffect, useState } from "react";
import { useUIStore } from "../../store/useUiStore";

interface ObserverContextType {
  registerSection: (id: string, element: HTMLElement | null) => void;
  getSectionState: (id: string) => boolean;
  sectionStates: Record<string, boolean>;
}

const ObserverContext = createContext<ObserverContextType | null>(null);

export function ObserverProvider({ children }: { children: React.ReactNode }) {
  const { setActiveSection } = useUIStore();
  const [sectionStates, setSectionStates] = useState<Record<string, boolean>>({});
  const observerRef = useRef<IntersectionObserver | null>(null);
  const sectionsRef = useRef<Map<string, HTMLElement>>(new Map());

  const registerSection = (id: string, element: HTMLElement | null) => {
    if (!element) return;

    sectionsRef.current.set(id, element);

    if (observerRef.current) {
      observerRef.current.observe(element);
    }
  };

  const getSectionState = (id: string): boolean => {
    return sectionStates[id] || false;
  };

  useEffect(() => {
    // Create unified intersection observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const updates: Record<string, boolean> = {};
        let mostVisibleEntry: IntersectionObserverEntry | null = null;
        let maxRatio = 0;

        entries.forEach((entry) => {
          const sectionId = entry.target.id;
          const isVisible = entry.isIntersecting && entry.intersectionRatio > 0.3;

          // Update animation states
          updates[sectionId] = isVisible;

          // Track most visible section for navigation
          if (entry.intersectionRatio > maxRatio) {
            maxRatio = entry.intersectionRatio;
            mostVisibleEntry = entry;
          }
        });

        // Update animation states
        setSectionStates(prev => ({ ...prev, ...updates }));

        // Update active section for navigation (only if significantly visible)
        if (mostVisibleEntry && maxRatio > 0.5) {
          const activeId = mostVisibleEntry.target.id as any;
          setActiveSection(activeId);
        }
      },
      {
        threshold: [0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.0],
        rootMargin: "-50px 0px -50px 0px"
      }
    );

    // Observe existing sections
    sectionsRef.current.forEach((element) => {
      observerRef.current?.observe(element);
    });

    return () => {
      observerRef.current?.disconnect();
    };
  }, [setActiveSection]);

  return (
    <ObserverContext.Provider value={{ registerSection, getSectionState, sectionStates }}>
      {children}
    </ObserverContext.Provider>
  );
}

export function useObserver() {
  const context = useContext(ObserverContext);
  if (!context) {
    throw new Error("useObserver must be used within ObserverProvider");
  }
  return context;
}

/**
 * Hook for section animation using the unified observer
 */
export function useSectionAnimation(sectionId: string) {
  const { registerSection, getSectionState } = useObserver();
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      registerSection(sectionId, elementRef.current);
    }
  }, [sectionId, registerSection]);

  return [elementRef, getSectionState(sectionId)] as const;
}
