import React, { createContext, useContext, useRef, useEffect, useState } from "react";
import { useUIStore } from "../../store/useUiStore";
import type { ObserverContextType } from "../types";



const ObserverContext = createContext<ObserverContextType | null>(null);

export function ObserverProvider({ children }: { children: React.ReactNode }) {
  const { setActiveSection } = useUIStore();
  const [sectionStates, setSectionStates] = useState<Record<string, boolean>>({});
  const observerRef = useRef<IntersectionObserver | null>(null);
  const sectionsRef = useRef<Map<string, HTMLElement>>(new Map());

  const registerSection = (id: string, element: HTMLElement | null) => {
    if (!element) return;

    sectionsRef.current.set(id, element);

    if (observerRef.current) {
      observerRef.current.observe(element);
    }
  };

  const getSectionState = (id: string): boolean => {
    return sectionStates[id] || false;
  };

  useEffect(() => {
    let debounceTimer: number;

    // Create unified intersection observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const updates: Record<string, boolean> = {};
        let mostVisibleEntry: IntersectionObserverEntry | null = null;
        let maxRatio = 0;

        entries.forEach((entry) => {
          const sectionId = entry.target.id;
          const isVisible = entry.isIntersecting && entry.intersectionRatio > 0.3;

          // Update animation states
          updates[sectionId] = isVisible;

          // Track most visible section for navigation
          if (entry.intersectionRatio > maxRatio) {
            maxRatio = entry.intersectionRatio;
            mostVisibleEntry = entry;
          }
        });

        // Update animation states immediately
        setSectionStates(prev => ({ ...prev, ...updates }));

        // Debounce active section updates to prevent flickering
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
          // Only update if section is significantly visible and stable
          if (mostVisibleEntry && maxRatio > 0.6) {
            const activeId = mostVisibleEntry.target.id as any;
            setActiveSection(activeId);
          }
        }, 100); // 100ms debounce
      },
      {
        threshold: [0, 0.1, 0.3, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
        rootMargin: "-80px 0px -80px 0px" // Larger margins for more stable detection
      }
    );

    // Observe existing sections
    sectionsRef.current.forEach((element) => {
      observerRef.current?.observe(element);
    });

    return () => {
      clearTimeout(debounceTimer);
      observerRef.current?.disconnect();
    };
  }, [setActiveSection]);

  return (
    <ObserverContext.Provider value={{ registerSection, getSectionState, sectionStates }}>
      {children}
    </ObserverContext.Provider>
  );
}

export function useObserver() {
  const context = useContext(ObserverContext);
  if (!context) {
    throw new Error("useObserver must be used within ObserverProvider");
  }
  return context;
}

/**
 * Hook for section animation using the unified observer
 */
export function useSectionAnimation(sectionId: string) {
  const { registerSection, getSectionState } = useObserver();
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      registerSection(sectionId, elementRef.current);
    }
  }, [sectionId, registerSection]);

  return [elementRef, getSectionState(sectionId)] as const;
}
