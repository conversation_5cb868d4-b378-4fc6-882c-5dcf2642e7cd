
import { Divider } from "primereact/divider";
import ProjectCards from "../components/ProjectCards";
import { ProjectData } from "../libs/constants";





export default function Projects() {
  return (
    <div
      className="flex h-full flex-column w-11 justify-content-center p-8   "
      style={{
        background: "rgba(0, 0, 0, 0.6)", color: "var(--text-color)", borderRadius: "5px",
        border: "2px solid var(--primary-color)", outline: "5px solid rgba(0,0,0,0.5)"
      }}
    >

      <h2 className="flex w-12 text-center justify-content-center text-2xl font-bold m-0">Active Projects</h2>
      <Divider className="mt-0 pt-0 mb-3" />
      <div className="flex flex-row justify-content-evenly">
        {ProjectData.map((project) => {
          return (
            <ProjectCards key={project.id} project={project} />
          );
        })}
      </div>

    </div>

  );
}
