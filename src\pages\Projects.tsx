import { useState } from "react";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { Divider } from "primereact/divider";
import { Chip } from "primereact/chip";


const projects = [
  {
    id: "crypto2",
    title: "Crypto App",
    url: "https://hicksdaniel.github.io/crypto2/",
    description: "A React app for tracking crypto prices and changes.",
    features: [
      "Real-time price updates",
      "Historical data charts",
      "Compare Favorite Coins over Time",
      "Dashboard with selectable charts",
    ],
    technologies: ["React", "PrimeFlex", "Zustand", "Chart.js"],
    image: "https://media.giphy.com/media/mHgyKfEn5XfOJ2aQCn/giphy.gif",
  },
  {
    id: "PokemonProject",
    title: "Poke App",
    url: "https://google.com",
    description: "A React app for displaying Pokémon stats and charts.",
    features: [
      "Pokémon stats visualization",
      "Responsive design",
      "Animations with Framer Motion",
    ],
    technologies: [
      "React",
      "CSS",
      "ContextAPI",
      "Chart.js",
      "HTML5",

    ],

    image: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExdjJhaTB3YXh0eTlkbXd0YnM4OGM1bHF2ZDlxdWZyYWFndTA1ZHNxNyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/LdzS4ccJZmWsbrSOoz/giphy.gif",

  },
];

export default function Projects() {
  const [hovered, setHovered] = useState<string | null>(null);

  return (
    <div
      className="flex align-items-center flex-column w-12  gap-1 px-0 py-4"
      style={{ width: "1200px", background: "var(--surface-)", color: "var(--text-color)" }}
    >
      <div className="grid col-12  justify-content-center">
        <h1 className="col-12 text-center">Active Projects</h1>
        {projects.map((project) => {
          const isHovered = hovered === project.id;


          const header = (
            <div className="flex w-12 flex-column align-items-center px-1  h-15rem">
              <h2 className="p-0 mt-3">{project.title}</h2>
              <Divider className="p-0 m-0" />
              <h4 className="p-2 my-2">{project.description}</h4>
              <ul className="w-19">
                {project.features.map((feature) => (
                  <li key={feature}>{feature}</li>
                ))}
              </ul>
            </div>
          );

          const footer = (
            <div className="flex justify-content-center gap-3 mb-5">
              <Button
                className="p-button-sm p-button-outlined"
                label="Open Website"
                icon="pi pi-globe"
                onClick={() => {
                  window.open(`${project.url}`, "_blank");
                }}
              />
              <Button
                label="Open in GitHub"
                icon="pi pi-github"
                className="p-button-sm p-button-outlined"
                onClick={() => {
                  window.open(`https://github.com/HicksDaniel/${project.id}`, "_blank");
                }}
              />
            </div>
          );
          return (
            <Card
              key={project.id}
              footer={footer}
              header={header}
              pt={{
                root: {},
                body: {
                  style: { width: "100%", padding: "5px" },
                },
                footer: {
                  style: { display: "flex", justifyContent: "center", alignItems: "" }
                }
              }}
              className="col-12 sm:col-11 md:col-6 lg:col-6 xl:col-6 flex-column align-items-center p-1 shadow-5"
            >
              <h4 className="p-card-subtitle text-center mt-0 w-12">
                Hover over to see Technologies Used
              </h4>
              <div
                className="flex relative w-full h-16rem sm:h-18rem md:h-24rem lg:h-18rem xl:h-24rem   p-0 m-0  "
                onMouseEnter={() => setHovered(project.id)}
                onMouseLeave={() => setHovered(null)}
                style={{


                  objectFit: "contain",
                }}
              >
                <img
                  src={project.image}
                  alt={project.title}
                  className="border-round-xl w-12 shadow-2"
                  style={{
                    minWidth: "18rem",
                    objectFit: "contain",
                  }}
                />

                <div className=" flex absolute w-12  h-full  bg-black-alpha-30 align-items-center justify-content-center">
                  {project.technologies.map((tech, i) => {
                    const total = project.technologies.length;
                    const step = (2 * Math.PI) / total;
                    const offset = total % 2 === 0 ? step / 1 : -Math.PI / 2;
                    const angle = (2 * Math.PI * i) / total + offset
                    const radius = isHovered ? 120 : 360;
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius;

                    return (
                      <div
                        className="flex align-items-center justify-content-center absolute text-white  px-2 font-bold border-round-2xl theme-primary-bg shadow-8"
                        style={{
                          left: `calc(50% + ${x}px)`,
                          top: `calc(50% + ${y}px)`,
                          transform: "translate(-50%, -50%)",
                          height: "2rem",
                          width: "7rem",
                          opacity: isHovered ? 1 : 0,
                          transition: "opacity 1.25s ease, left 1s ease, top 1s ease",

                        }}>
                        {tech}


                      </div>
                    );
                  })}
                </div>

              </div>
            </Card>
          );
        })}
      </div>
    </div>

  );
}
