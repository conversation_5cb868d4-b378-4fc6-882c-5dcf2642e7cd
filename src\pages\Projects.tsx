import { useState } from "react";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { Divider } from "primereact/divider";
import { Chip } from "primereact/chip";


const projects = [
  {
    id: "crypto2",
    title: "Crypto App",
    url: "https://hicksdaniel.github.io/crypto2/",
    description: "A React app for tracking crypto prices and changes.",
    features: [
      "Real-time price updates",
      "Historical data charts",
      "Compare Favorite Coins over Time",
      "Dashboard with selectable charts",
    ],
    technologies: ["React", "PrimeFlex", "Zustand", "Chart.js"],
    image: "https://media.giphy.com/media/mHgyKfEn5XfOJ2aQCn/giphy.gif",
  },
  {
    id: "PokemonProject",
    title: "Poke App",
    url: "https://google.com",
    description: "A React app for displaying Pokémon stats and charts.",
    features: [
      "Pokémon stats visualization",
      "Responsive design",
      "Animations with Framer Motion",
    ],
    technologies: [
      "React",
      "CSS",
      "ContextAPI",
      "Chart.js",
      "HTML5",

    ],

    image: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExdjJhaTB3YXh0eTlkbXd0YnM4OGM1bHF2ZDlxdWZyYWFndTA1ZHNxNyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/LdzS4ccJZmWsbrSOoz/giphy.gif",

  },
];

export default function Projects() {
  const [hovered, setHovered] = useState<string | null>(null);

  return (
    <div
      className="flex h-full flex-column w-11 justify-content-center    "
      style={{
        background: "rgba(0, 0, 0, 0.75)", color: "var(--text-color)", borderRadius: "5px",
        border: "2px solid var(--primary-color)", outline: "5px solid rgba(0,0,0,0.5)"
      }}
    >

      <h2 className="flex w-12 text-center justify-content-center text-2xl font-bold m-0">Active Projects</h2>
      <Divider className="mt-0 pt-0 mb-3" />
      <div
        className="flex flex-row justify-content-evenly ">

        {projects.map((project) => {
          const isHovered = hovered === project.id;


          const header = (
            <div className="flex w-12 flex-column align-items-center px-1 ">
              <h2 className="p-0 my-1">{project.title}</h2>
              <Divider className="p-0 m-0" />
              <h4 className="p-0 text-sm my-2">{project.description}</h4>
              <ul className="p-0 text-sm mt-0">
                {project.features.map((feature) => (
                  <li key={feature}>{feature}</li>
                ))}
              </ul>
            </div>
          );

          const footer = (
            <div className="flex justify-content-center gap-3 mb-5">
              <Button
                className="p-button-sm p-button-outlined"
                label="Open Website"
                icon="pi pi-globe"
                onClick={() => {
                  window.open(`${project.url}`, "_blank");
                }}
              />
              <Button
                label="Open in GitHub"
                icon="pi pi-github"
                className="p-button-sm p-button-outlined"
                onClick={() => {
                  window.open(`https://github.com/HicksDaniel/${project.id}`, "_blank");
                }}
              />
            </div>
          );
          return (
            <Card
              key={project.id}
              footer={footer}
              header={header}


              style={{
                height: "100%",
                background: "rgba(0, 0, 0, 0.75)", color: "var(--text-color)", borderRadius: "5px",
                border: "2px solid var(--primary-color)", outline: "5px solid rgba(0,0,0,0.5)"
              }}
              className="flex flex-column w-6 mx-3 align-items-between justify-content-between p-1 shadow-5"
            >
              <h4 className="p-card-subtitle text-center text-xs mt-0 w-12">
                Hover over to see Technologies Used
              </h4>
              <div
                className="flex relative w-full "
                onMouseEnter={() => setHovered(project.id)}
                onMouseLeave={() => setHovered(null)}

              >
                <img
                  src={project.image}
                  alt={project.title}
                  className="border-round-xl  w-12 shadow-2"
                  style={{
                    minWidth: "18rem",
                    objectFit: "contain",
                  }}
                />

                <div className=" flex absolute w-12  h-full  bg-black-alpha-30 align-items-center justify-content-center">
                  {project.technologies.map((tech, i) => {
                    const total = project.technologies.length;
                    const step = (2 * Math.PI) / total;
                    const offset = total % 2 === 0 ? step / 1 : -Math.PI / 2;
                    const angle = (2 * Math.PI * i) / total + offset
                    const radius = isHovered ? 100 : 360;
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius;

                    return (
                      <div
                        className="flex align-items-center justify-content-center absolute text-white  px-2 font-bold border-round-2xl theme-primary-bg shadow-8"
                        style={{
                          left: `calc(50% + ${x}px)`,
                          top: `calc(50% + ${y}px)`,
                          transform: "translate(-50%, -50%)",
                          height: "2rem",
                          width: "6.5rem",
                          opacity: isHovered ? 1 : 0,
                          transition: "opacity 1.25s ease, left 1s ease, top 1s ease",

                        }}>
                        {tech}


                      </div>
                    );
                  })}
                </div>

              </div>
            </Card>
          );
        })}
      </div>

    </div>

  );
}
